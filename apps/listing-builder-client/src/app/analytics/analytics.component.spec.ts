import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router, ActivatedRoute } from '@angular/router';
import { BehaviorSubject, of } from 'rxjs';
import { AnalyticsComponent, InsightsTab } from './analytics.component';
import { BingInsightsService } from '../bing-insights/bing-insights.service';
import { ListingSyncService, AGIDTOKEN } from '@vendasta/local-seo';
import { ListingProductsApiService } from '@vendasta/listing-products';
import { AccountGroupApiService } from '@vendasta/account-group';
import { AppPartnerService } from '@galaxy/marketplace-apps';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { TranslateModule } from '@ngx-translate/core';
import { MatTabsModule } from '@angular/material/tabs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('AnalyticsComponent - Tab Highlighting Fix', () => {
  let component: AnalyticsComponent;
  let fixture: ComponentFixture<AnalyticsComponent>;
  let mockRouter: any;
  let mockBingInsightsService: any;
  let mockListingSyncService: any;
  let mockListingProductsService: any;
  let mockAccountGroupService: any;
  let mockAppPartnerService: any;
  let mockProductAnalyticsService: any;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    // Create spies for all dependencies using Jest
    mockRouter = {
      url: '/analytics/google',
      events: of(),
      navigateByUrl: jest.fn()
    };

    mockBingInsightsService = {
      isBingDataAvailable$: of(true)
    };

    mockListingSyncService = {
      syncData$: of([])
    };

    mockListingProductsService = {
      isLocalSeoProActiveForAccount: jest.fn().mockReturnValue(of({ isActive: false }))
    };

    mockAccountGroupService = {
      getMulti: jest.fn().mockReturnValue(of({ accountGroups: [] }))
    };

    mockAppPartnerService = {
      getAppSettings: jest.fn().mockReturnValue(of(null))
    };

    mockProductAnalyticsService = {
      trackEvent: jest.fn()
    };

    mockActivatedRoute = {
      params: of({}),
      queryParams: of({}),
      snapshot: { params: {}, queryParams: {} }
    };

    await TestBed.configureTestingModule({
      declarations: [AnalyticsComponent],
      imports: [
        TranslateModule.forRoot(),
        MatTabsModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: AGIDTOKEN, useValue: of('test-agid') },
        { provide: Router, useValue: mockRouter },
        { provide: BingInsightsService, useValue: mockBingInsightsService },
        { provide: ListingSyncService, useValue: mockListingSyncService },
        { provide: ListingProductsApiService, useValue: mockListingProductsService },
        { provide: AccountGroupApiService, useValue: mockAccountGroupService },
        { provide: AppPartnerService, useValue: mockAppPartnerService },
        { provide: ProductAnalyticsService, useValue: mockProductAnalyticsService },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AnalyticsComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle Bing tab highlighting when upgradeToProEnabled is false', (done) => {
    // Mock to make upgradeToProEnabled false
    // isFreeEdition = true (isActive = false), and hideUpgradeCta = true
    mockListingProductsService.isLocalSeoProActiveForAccount.mockReturnValue(of({ isActive: false }));
    mockAppPartnerService.getAppSettings.mockReturnValue(of({
      editionChange: { hideUpgradeCta: true }
    }));

    // Simulate navigating to Bing tab when upgrade is not enabled
    mockRouter.url = '/analytics/bing';

    fixture.detectChanges();

    // Wait for observables to emit
    setTimeout(() => {
      component.selectedTabIndex$.subscribe(selectedIndex => {
        console.log('🧪 Test - selectedIndex:', selectedIndex);
        // When upgradeToProEnabled is false and we're on Bing route,
        // selectedIndex should default to Google (0) to prevent highlighting issues
        expect(selectedIndex).toBe(InsightsTab.Google);
        done();
      });
    }, 100);
  });

  it('should handle Bing tab highlighting when upgradeToProEnabled is true', (done) => {
    // Mock upgradeToProEnabled to be true
    mockListingProductsService.isLocalSeoProActiveForAccount.mockReturnValue(of({ isActive: true }));
    mockRouter.url = '/analytics/bing';

    fixture.detectChanges();

    // Wait for observables to emit
    setTimeout(() => {
      component.selectedTabIndex$.subscribe(selectedIndex => {
        console.log('🧪 Test - selectedIndex when pro enabled:', selectedIndex);
        // When upgradeToProEnabled is true and we're on Bing route,
        // selectedIndex should be Bing (1)
        expect(selectedIndex).toBe(InsightsTab.Bing);
        done();
      });
    }, 100);
  });

  it('should handle Google tab highlighting correctly', (done) => {
    mockRouter.url = '/analytics/google';

    fixture.detectChanges();

    // Wait for observables to emit
    setTimeout(() => {
      component.selectedTabIndex$.subscribe(selectedIndex => {
        console.log('🧪 Test - selectedIndex for Google:', selectedIndex);
        // Google tab should always work regardless of upgradeToProEnabled
        expect(selectedIndex).toBe(InsightsTab.Google);
        done();
      });
    }, 100);
  });
});
