import { Component, Inject, OnInit, OnDestroy } from '@angular/core';
import { Observable, of, throwError, Subject, combineLatest, BehaviorSubject } from 'rxjs';
import { catchError, map, shareReplay, switchMap, take, takeUntil, startWith } from 'rxjs/operators';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { AGIDTOKEN, appID, BingSourceID, ListingSyncService } from '@vendasta/local-seo';
import { AppSettings, EditionUpgradeAction } from '@vendasta/marketplace-apps';
import { BingInsightsService } from '../bing-insights/bing-insights.service';
import {
  IsLocalSEOProActiveForAccountRequest,
  IsLocalSEOProActiveForAccountResponse,
  ListingProductsApiService,
} from '@vendasta/listing-products';
import { AccountGroupApiService, GetMultiRequest, ProjectionFilter } from '@vendasta/account-group';
import { AppPartnerService } from '@galaxy/marketplace-apps';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
export enum InsightsTab {
  Google,
  Bing,
}

@Component({
  selector: 'app-analytics',
  templateUrl: './analytics.component.html',
  styleUrls: ['./analytics.component.scss'],
  standalone: false,
})
export class AnalyticsComponent implements OnInit, OnDestroy {
  pageTitleKey$ = of('ANALYTICS.NAME');
  activeTab: InsightsTab = InsightsTab.Google;
  private activeTab$ = new BehaviorSubject<InsightsTab>(InsightsTab.Google);
  readonly googleIconUrl = 'https://www.cdnstyles.com/static/images/source-icons/Google.svg';
  readonly bingIconUrl = 'https://www.cdnstyles.com/static/images/source-icons/Bing.svg';
  upgradeToProEnabled$: Observable<boolean>;
  public isFreeEdition$: Observable<boolean>;
  isBingDataAvailable$: Observable<boolean>;
  isBingSyncEnable$: Observable<boolean>;
  isBingListingAvailable$: Observable<boolean>;
  selectedTabIndex$: Observable<number>;
  private destroy$ = new Subject<void>();

  constructor(
    @Inject(AGIDTOKEN) private agid$: Observable<string>,
    private bingInsightsService: BingInsightsService,
    private listingSyncService: ListingSyncService,
    private listingProducts: ListingProductsApiService,
    private accountGroupService: AccountGroupApiService,
    private appPartnerService: AppPartnerService,
    private productAnalyticsService: ProductAnalyticsService,
    public router: Router,
    private route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.initializeIsFreeEdition$();
    this.syncBingStatus();

    this.upgradeToProEnabled$ = this.isFreeEdition$.pipe(
      switchMap((isFreeEdition) => {
        if (isFreeEdition) {
          return this.upgradeToProAppSettings$().pipe(map((settings) => !settings.editionChange.hideUpgradeCta));
        }
        return of(true);
      }),
    );

    // Initialize selectedTabIndex$ to handle tab highlighting correctly
    this.selectedTabIndex$ = combineLatest([
      this.upgradeToProEnabled$.pipe(startWith(false)),
      this.activeTab$.asObservable()
    ]).pipe(
      map(([upgradeToProEnabled, activeTab]) => {
        console.log('🐛 Tab Debug - upgradeToProEnabled:', upgradeToProEnabled, 'activeTab:', activeTab, 'activeTabName:', InsightsTab[activeTab]);

        // If Bing tab is not available (upgradeToProEnabled is false) and we're trying to show Bing tab,
        // default to Google tab (index 0)
        if (!upgradeToProEnabled && activeTab === InsightsTab.Bing) {
          console.log('🐛 Tab Debug - Bing tab not available, defaulting to Google');
          return InsightsTab.Google;
        }

        // If Bing tab is available and we want Bing, return Bing index (1)
        // If we want Google, return Google index (0)
        const selectedIndex = activeTab;
        console.log('🐛 Tab Debug - Selected index:', selectedIndex);
        return selectedIndex;
      }),
      shareReplay(1)
    );

    this.productAnalyticsService.trackEvent('google-insight-tab', 'listing-builder-client', 'onload', 0);

    this.isBingDataAvailable$ = this.bingInsightsService.isBingDataAvailable$;
    this.router.events.pipe(takeUntil(this.destroy$)).subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.updateActiveTabFromRoute();
      }
    });

    this.updateActiveTabFromRoute();
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private syncBingStatus(): void {
    this.isBingSyncEnable$ = this.listingSyncService.syncData$.pipe(
      map((syncDataArray) => {
        const syncData = syncDataArray.find((syncData) => syncData.sourceId === BingSourceID);
        return !!syncData?.isSyncingEnabled;
      }),
    );
    this.isBingListingAvailable$ = this.listingSyncService.syncData$.pipe(
      map((syncDataArray) => {
        return syncDataArray.some((syncData) => syncData.listingUrl != '' && syncData.listingId != '');
      }),
    );
  }

  private upgradeToProAppSettings$(): Observable<AppSettings> {
    const defaultAppSettings = new AppSettings({
      editionChange: {
        editionUpgradeAction: EditionUpgradeAction.EDITION_UPGRADE_ACTION_CONTACT_SALES,
      },
      branding: { enabled: false },
    });

    const partnerMarket$ = this.agid$.pipe(
      switchMap((businessID) =>
        businessID
          ? this.accountGroupService
              .getMulti(
                new GetMultiRequest({
                  accountGroupIds: [businessID],
                  projectionFilter: new ProjectionFilter({
                    accountGroupExternalIdentifiers: true,
                  }),
                }),
              )
              .pipe(
                map((resp) => {
                  const identifiers = resp.accountGroups[0]?.accountGroup?.accountGroupExternalIdentifiers;
                  const partnerId = identifiers?.partnerId;
                  const marketId = identifiers?.marketId;
                  return { partnerId, marketId };
                }),
              )
          : of(null),
      ),
      shareReplay(1),
    );

    return partnerMarket$.pipe(
      switchMap((pm) =>
        pm
          ? this.appPartnerService.getAppSettings(appID, pm.partnerId, pm.marketId).pipe(
              catchError((err) => (err.status === 404 ? of(defaultAppSettings) : throwError(err))),
              map((response) => response || defaultAppSettings),
            )
          : of(defaultAppSettings),
      ),
      shareReplay(1),
    );
  }

  private initializeIsFreeEdition$(): void {
    this.isFreeEdition$ = this.agid$.pipe(
      take(1),
      switchMap((accountGroupId) =>
        this.listingProducts.isLocalSeoProActiveForAccount(
          new IsLocalSEOProActiveForAccountRequest({ accountGroupId }),
        ),
      ),
      map((response: IsLocalSEOProActiveForAccountResponse) => !response.isActive),
    );
  }

  onTabSwitch(index: InsightsTab): void {
    const route = index === InsightsTab.Google ? 'google' : 'bing';
    const currentUrl = this.router.url;
    let newUrl: string;
    if (currentUrl.endsWith('/analytics')) {
      newUrl = `${currentUrl}/${route}`;
    } else if (currentUrl.includes('/analytics/')) {
      const urlSegments = currentUrl.split('/');
      urlSegments[urlSegments.length - 1] = route;
      newUrl = urlSegments.join('/');
    } else {
      newUrl = `${currentUrl}/${route}`;
    }

    this.router.navigateByUrl(newUrl);
    if (index === InsightsTab.Google) {
      this.productAnalyticsService.trackEvent('google-insight-tab', 'listing-builder-client', 'change', 0, {
        tab: index,
      });
    } else {
      this.productAnalyticsService.trackEvent('bing-insight-tab', 'listing-builder-client', 'change', 1, {
        tab: index,
      });
    }
  }

  isActiveTabGoogle(): boolean {
    return this.activeTab === InsightsTab.Google;
  }

  private updateActiveTabFromRoute(): void {
    const currentUrl = this.router.url;
    console.log('🐛 Tab Debug - updateActiveTabFromRoute called with URL:', currentUrl);

    if (currentUrl.includes('/analytics/bing')) {
      this.activeTab = InsightsTab.Bing;
      this.activeTab$.next(InsightsTab.Bing);
      console.log('🐛 Tab Debug - Set activeTab to Bing');
    } else {
      this.activeTab = InsightsTab.Google;
      this.activeTab$.next(InsightsTab.Google);
      console.log('🐛 Tab Debug - Set activeTab to Google');
    }
  }
}
