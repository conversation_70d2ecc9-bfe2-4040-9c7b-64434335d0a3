<app-page [pageTitle]="pageTitleKey$ | async | translate">
  <!-- Debug Info -->
  <div style="background: #f0f0f0; padding: 8px; margin-bottom: 16px; font-size: 12px; border-radius: 4px;">
    🐛 Debug Info:
    activeTab={{ activeTab }} ({{ activeTab === 0 ? 'Google' : 'Bing' }}) |
    selectedIndex={{ selectedTabIndex$ | async }} |
    upgradeToProEnabled={{ upgradeToProEnabled$ | async }} |
    URL={{ router.url }}
  </div>

  <mat-tab-group
    mat-stretch-tabs="false"
    mat-align-tabs="start"
    [selectedIndex]="selectedTabIndex$ | async"
    (selectedIndexChange)="onTabSwitch($event)"
  >
    <mat-tab>
      <ng-template mat-tab-label>
        <span class="tab-label-spacer"></span>
        <img [src]="googleIconUrl" alt="Google Icon" width="24" height="24" class="tab-icon" />
        <span class="tab-label-text">{{ 'GOOGLE_INSIGHTS.TITLE' | translate }}</span>
        <span class="tab-label-extra-spacer"></span>
      </ng-template>
    </mat-tab>
    @if (upgradeToProEnabled$ | async) {
      <mat-tab>
        <ng-template mat-tab-label>
          <span class="tab-label-spacer"></span>
          <img [src]="bingIconUrl" alt="Bing Icon" width="24" height="24" class="tab-icon" />
          <span class="tab-label-text">{{ 'BING_INSIGHTS.TITLE' | translate }}</span>
          <span class="tab-label-extra-spacer"></span>
        </ng-template>
      </mat-tab>
    }
  </mat-tab-group>
  @if (isActiveTabGoogle()) {
    <br />
    <app-google-insights></app-google-insights>
  } @else {
    <br />
    @if (isFreeEdition$ | async) {
      <app-bing-empty-states
        [upgradeToProEnabled]="upgradeToProEnabled$ | async"
        [isStanderdUser]="true"
        [isBingSyncEnable$]="isBingSyncEnable$"
        [isBingListingAvailable$]="isBingListingAvailable$"
      ></app-bing-empty-states>
    } @else {
      @if ((isBingDataAvailable$ | async) && (isBingSyncEnable$ | async)) {
        <app-bing-insights></app-bing-insights>
      } @else {
        <app-bing-empty-states
          [upgradeToProEnabled]="upgradeToProEnabled$ | async"
          [isStanderdUser]="false"
          [isBingSyncEnable$]="isBingSyncEnable$"
          [isBingListingAvailable$]="isBingListingAvailable$"
        ></app-bing-empty-states>
      }
    }
  }
</app-page>
