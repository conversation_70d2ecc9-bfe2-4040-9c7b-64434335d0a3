<app-page [pageTitle]="pageTitleKey$ | async | translate">
  <mat-tab-group
    mat-stretch-tabs="false"
    mat-align-tabs="start"
    [selectedIndex]="activeTab"
    (selectedIndexChange)="onTabSwitch($event)"
  >
    <mat-tab>
      <ng-template mat-tab-label>
        <span class="tab-label-spacer"></span>
        <img [src]="googleIconUrl" alt="Google Icon" width="24" height="24" class="tab-icon" />
        <span class="tab-label-text">{{ 'GOOGLE_INSIGHTS.TITLE' | translate }}</span>
        <span class="tab-label-extra-spacer"></span>
      </ng-template>
    </mat-tab>
    <mat-tab [disabled]="(upgradeToProEnabled$ | async) === false">
      <ng-template mat-tab-label>
        <span class="tab-label-spacer"></span>
        <img [src]="bingIconUrl" alt="Bing Icon" width="24" height="24" class="tab-icon" />
        <span class="tab-label-text">{{ 'BING_INSIGHTS.TITLE' | translate }}</span>
        <span class="tab-label-extra-spacer"></span>
      </ng-template>
    </mat-tab>
  </mat-tab-group>
  @if (isActiveTabGoogle()) {
    <br />
    <app-google-insights></app-google-insights>
  } @else {
    <br />
    @if (isFreeEdition$ | async) {
      <app-bing-empty-states
        [upgradeToProEnabled]="upgradeToProEnabled$ | async"
        [isStanderdUser]="true"
        [isBingSyncEnable$]="isBingSyncEnable$"
        [isBingListingAvailable$]="isBingListingAvailable$"
      ></app-bing-empty-states>
    } @else {
      @if ((isBingDataAvailable$ | async) && (isBingSyncEnable$ | async)) {
        <app-bing-insights></app-bing-insights>
      } @else {
        <app-bing-empty-states
          [upgradeToProEnabled]="upgradeToProEnabled$ | async"
          [isStanderdUser]="false"
          [isBingSyncEnable$]="isBingSyncEnable$"
          [isBingListingAvailable$]="isBingListingAvailable$"
        ></app-bing-empty-states>
      }
    }
  }
</app-page>
